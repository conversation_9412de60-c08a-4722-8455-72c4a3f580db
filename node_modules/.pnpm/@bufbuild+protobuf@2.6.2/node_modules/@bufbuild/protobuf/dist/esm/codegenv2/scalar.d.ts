import { ScalarType } from "../descriptors.js";
/**
 * Return the TypeScript type (as a string) for the given scalar type.
 */
export declare function scalarTypeScriptType(scalar: ScalarType, longAsString: boolean): "string" | "boolean" | "bigint" | "bigint | string" | "Uint8Array" | "number";
/**
 * Return the JSON type (as a string) for the given scalar type.
 */
export declare function scalarJsonType(scalar: ScalarType): "string" | "boolean" | "number" | `number | "NaN" | "Infinity" | "-Infinity"`;
