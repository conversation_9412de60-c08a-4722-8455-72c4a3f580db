import type { Message } from "../types.js";
import type { DescFile } from "../descriptors.js";
import type { GenMessage } from "./types.js";
import type { JsonValue } from "../json-value.js";
/**
 * Hydrate a message descriptor.
 *
 * @private
 */
export declare function messageDesc<Shape extends Message, JsonType extends JsonValue = JsonValue>(file: DescFile, path: number, ...paths: number[]): GenMessage<Shape, JsonType>;
