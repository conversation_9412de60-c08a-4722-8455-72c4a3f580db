import { SubscribeUpdate } from '@kdt-farm/solana-grpc-client/yellowstone'
import { sendRawMessage } from '../../modules/worker/utils/workers'
import { handleGeyserTransaction } from './transaction'

export function handleGeyserMessage(message: SharedArrayBuffer) {
    const data = SubscribeUpdate.decode(new Uint8Array(message))

    if (data.filters.includes('slot') && data.slot) {
        return sendRawMessage({ type: 'geyser:slot:update', data: data.slot })
    }

    if (data.filters.includes('blockMeta') && data.blockMeta) {
        return sendRawMessage({ type: 'geyser:slot:blockMeta', data: data.blockMeta })
    }

    if (data.filters.includes('transaction') && data.transaction) {
        return handleGeyserTransaction(data.transaction)
    }
}
