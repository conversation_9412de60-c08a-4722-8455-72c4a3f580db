import type { Awaitable } from '@kdt310722/utils/promise'
import type { SlotStatus, SubscribeUpdateTransaction } from '@kdt-farm/solana-grpc-client/yellowstone'
import type { UnixTimestamp } from '@solana/kit'
import type { Transaction } from '../../modules/rpc-client/types'
import { threadId } from 'node:worker_threads'
import { isNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { config } from '../../config'
import { createChildLogger } from '../../core/logger'
import { slotManager } from '../../core/slot'
import { BASE58_CODEC, toTransaction } from '../../modules/geyser/utils/formatters/to-transaction'

export type GeyserTransactionHandler = (transaction: Transaction) => Awaitable<void>

const logger = createChildLogger('workers:handlers:transaction')
const statusWaiters: Record<string, Promise<SlotStatus | undefined>> = {}
const timestampWaiters: Record<string, Promise<bigint | undefined>> = {}
const transactionHandlers = new Set<GeyserTransactionHandler>()

export function addTransactionHandler(handler: GeyserTransactionHandler) {
    transactionHandlers.add(handler)
}

export async function waitForSlotStatus(slot: bigint) {
    return statusWaiters[String(slot)] ??= slotManager.waitForStatus(slot, config.geyser.commitment).finally(() => {
        delete statusWaiters[String(slot)]
    })
}

export async function waitForSlotTimestamp(slot: bigint) {
    const timestamp = await (timestampWaiters[String(slot)] ??= slotManager.waitForTimestamp(slot).finally(() => {
        delete timestampWaiters[String(slot)]
    }))

    return timestamp as UnixTimestamp | undefined
}

async function handleTransaction(transaction: Transaction) {
    if (isNullish(slotManager.firstFullSlot) || transaction.slot < slotManager.firstFullSlot.slot) {
        return
    }

    const success = await waitForSlotStatus(transaction.slot).then(() => true).catch(() => tap(false, () => {
        logger.warn('Failed to wait for slot status', { slot: transaction.slot, signature: transaction.signature, worker: threadId })
    }))

    if (!success) {
        return
    }

    if (isNullish(transaction.blockTime)) {
        transaction.blockTime = await waitForSlotTimestamp(transaction.slot)
    }

    for (const handler of transactionHandlers) {
        await handler(transaction)
    }
}

export function handleGeyserTransaction(data: SubscribeUpdateTransaction) {
    if (isNullish(data.transaction)) {
        return
    }

    const signature = BASE58_CODEC.decode(data.transaction.signature)

    handleTransaction(toTransaction(data.slot, data.transaction)).catch((error) => {
        logger.exit(1, 'fatal', 'Failed to handle geyser transaction', error, { signature, worker: threadId })
    })
}
