import { threadId } from 'node:worker_threads'
import { highlight } from '@kdt310722/logger'
import { isObject } from '@kdt310722/utils/object'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { slotManager } from '../core/slot'
import { initializeWorker } from '../modules/worker/utils/workers'
import { handleGeyserMessage } from './handlers/geyser'
import { handleRaydiumTransactions } from './handlers/raydium'
import { addTransactionHandler } from './handlers/transaction'

const logger = createChildLogger(`worker:${threadId}`)

function handleTask() {
    //
}

addTransactionHandler(async (transaction) => {
    await handleRaydiumTransactions([transaction])
})

function handleUnhandledMessage(message: unknown) {
    if (message instanceof SharedArrayBuffer) {
        return handleGeyserMessage(message)
    }

    if (isObject(message) && message.type === 'geyser:slot:update') {
        return slotManager.updateSlot(message.slot, message.data)
    }

    logger.warn('Unhandled worker message', { message, threadId })
}

async function init() {
    database.setOptions({
        dropSchema: false,
        synchronize: false,
        migrationsRun: false,
    })

    await database.initialize()
}

initializeWorker(init, handleTask, {
    onStarted: () => logger.info(`Worker ${highlight(threadId)} started!`),
    onStartFailed: (error) => logger.error(`Worker ${highlight(threadId)} failed to start`, error),
    onUnhandledMessage: handleUnhandledMessage,
})
