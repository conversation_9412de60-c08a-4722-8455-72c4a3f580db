import type { Token } from '../entities/token'
import type { Trade } from '../modules/raydium-launchpad/entities/trade'
import { isMainThread } from 'node:worker_threads'
import { highlight } from '@kdt310722/logger'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { format, formatNanoseconds } from '@kdt310722/utils/number'
import { createDeferred, type DeferredPromise } from '@kdt310722/utils/promise'
import { config } from '../config'
import { database } from '../core/database'
import { createChildLogger } from '../core/logger'
import { RaydiumLaunchpad } from '../modules/raydium-launchpad/raydium-launchpad'
import { sendRawMessage } from '../modules/worker/utils/workers'

const logger = createChildLogger('common:raydium')
export const raydium = new RaydiumLaunchpad(database, config.raydium)

raydium.on('unhandledInstruction', (type, _, transaction) => logger.warn(`Unhandled instruction type: ${highlight(type)}`, { signature: transaction.signature }))
raydium.on('unhandledGlobalConfig', (address, transaction) => logger.warn(`Unhandled global config: ${highlight(address)}`, { signature: transaction.signature }))
raydium.on('unhandledPlatformConfig', (address, transaction) => logger.warn(`Unhandled platform config: ${highlight(address)}`, { signature: transaction.signature }))
raydium.on('unhandledQuoteToken', (address, transaction) => logger.warn(`Unhandled quote token: ${highlight(address)}`, { signature: transaction.signature }))

const tokenHandlers = new Set<(token: Token) => void>()
const tradeHandlers = new Set<(trade: Trade) => void>()

export function addTokenHandler(handler: (token: Token) => void) {
    tokenHandlers.add(handler)
}

export function addTradeHandler(handler: (trade: Trade) => void) {
    tradeHandlers.add(handler)
}

let firstReceivedSlot: bigint | undefined

export function getFirstReceivedSlot() {
    return firstReceivedSlot
}

const firstReceivedSlotHandlers = new Set<(slot: bigint) => void>()
const firstReceivedSlotPromise: DeferredPromise<bigint> = createDeferred()

export function onFirstReceivedSlot(handler: (slot: bigint) => void) {
    firstReceivedSlotHandlers.add(handler)
}

export function setFirstReceivedSlot(slot: bigint) {
    if (isNullish(firstReceivedSlot)) {
        firstReceivedSlotPromise.resolve(firstReceivedSlot = slot)

        for (const handler of firstReceivedSlotHandlers) {
            handler(slot)
        }
    }
}

export async function waitForFirstReceivedSlot() {
    return firstReceivedSlotPromise
}

if (!isMainThread) {
    raydium.on('token', (token) => {
        sendRawMessage({ type: 'raydium:token', data: token })
        setFirstReceivedSlot(BigInt(token.createdAtSlot))
    })

    raydium.on('trade', (trade) => {
        sendRawMessage({ type: 'raydium:trade', data: trade })
        setFirstReceivedSlot(BigInt(trade.slot))
    })
}

export function handleToken(token: Token) {
    for (const handler of tokenHandlers) {
        handler(token)
    }

    setFirstReceivedSlot(BigInt(token.createdAtSlot))
}

export function handleTrade(trade: Trade) {
    for (const handler of tradeHandlers) {
        handler(trade)
    }

    setFirstReceivedSlot(BigInt(trade.slot))
}

const cleanup = async () => {
    const timer = tap(logger.createTimer(), () => logger.info(`Cleaning up tokens and trades older than ${highlight(formatNanoseconds(BigInt(config.cleanup.maxTokenAge * 1e6)))}...`))

    await raydium.cleanup(config.cleanup.maxTokenAge).then(({ tokens, trades }) => {
        const tokensMessage = notNullish(tokens) ? `${highlight(format(tokens))} tokens` : null
        const tradesMessage = notNullish(trades) ? `${highlight(format(trades))} trades` : null
        const message = [tokensMessage, tradesMessage].filter(notNullish).join(' and ')

        logger.stopTimer(timer, 'info', `Cleaned up${message.length > 0 ? ` ${message}` : ''}!`)
    })
}

export async function initializeRaydium() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing Raydium...'))

    await cleanup().then(() => setInterval(() => cleanup().catch((error) => logger.error('Failed to cleanup raydium data', error)), config.cleanup.interval)).then(() => {
        logger.stopTimer(timer, 'info', 'Raydium initialized!')
    })
}
