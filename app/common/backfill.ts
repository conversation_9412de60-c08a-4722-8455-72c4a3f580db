import { isMainThread } from 'node:worker_threads'
import { highlight } from '@kdt310722/logger'
import { notNullish } from '@kdt310722/utils/common'
import { tap } from '@kdt310722/utils/function'
import { createChildLogger } from '../core/logger'
import { onGap } from '../core/slot'
import { workerPool } from '../core/worker'
import { raydium, waitForFirstReceivedSlot } from './raydium'

const logger = createChildLogger('common:backfill')

function handleGap(startSlot: bigint, endSlot: bigint) {
    logger.warn(`Gap found: ${highlight(startSlot)} -> ${highlight(endSlot)}`)

    const pools = workerPool.size
    const slotsPerPool = Math.ceil(Number(endSlot - startSlot + 1n) / pools)
}

if (isMainThread) {
    onGap((startSlot, endSlot) => handleGap(startSlot - 1n, endSlot + 1n))
}

export async function initializeBackfill() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing backfill...'))
    const startSlot = await waitForFirstReceivedSlot()
    const endSlot = await raydium.getLastKnownSlot()

    if (notNullish(endSlot)) {
        handleGap(startSlot, endSlot)
    }

    logger.stopTimer(timer, 'info', 'Backfill initialized!')
}
