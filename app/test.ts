const startSlot = 1
const endSlot = 201
const minSlotsPerPool = 100

const totalSlots = endSlot - startSlot + 1

function createPoolsWithMinSlots(startSlot: number, endSlot: number, minSlotsPerPool: number) {
    const totalSlots = endSlot - startSlot + 1
    const pools = []

    let currentSlot = startSlot
    let poolIndex = 0

    while (currentSlot <= endSlot) {
        const remainingSlots = endSlot - currentSlot + 1

        let slotsForThisPool: number

        if (remainingSlots <= minSlotsPerPool) {
            slotsForThisPool = remainingSlots
        } else {
            const remainingPools = Math.ceil(remainingSlots / minSlotsPerPool)

            if (remainingPools === 1) {
                slotsForThisPool = remainingSlots
            } else {
                slotsForThisPool = minSlotsPerPool
            }
        }

        const poolStart = currentSlot
        const poolEnd = currentSlot + slotsForThisPool - 1

        pools.push({
            index: poolIndex,
            start: poolStart,
            end: poolEnd,
            slots: slotsForThisPool,
        })

        console.log(`Pool ${poolIndex}: ${poolStart} -> ${poolEnd} (${slotsForThisPool} slots)`)

        currentSlot += slotsForThisPool
        poolIndex++
    }

    return pools
}

console.log(`Total slots: ${totalSlots}`)
console.log(`Min slots per pool: ${minSlotsPerPool}`)
console.log('---')

const poolsResult = createPoolsWithMinSlots(startSlot, endSlot, minSlotsPerPool)

console.log('---')
console.log(`Total pools created: ${poolsResult.length}`)
console.log(`Slots distribution: ${poolsResult.map((p) => p.slots).join(', ')}`)
