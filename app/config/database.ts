import { isString, trim } from '@kdt310722/utils/string'
import { z } from 'zod'
import { bool } from '../utils/schemas/bool'

const levels = ['query', 'schema', 'error', 'warn', 'info', 'log', 'migration'] as const
const logging = z.preprocess((value) => (isString(value) ? (value === 'all' ? levels : value.split(',').map((i) => trim(i))) : value), z.enum(levels).array())

const base = z.object({
    logging: logging.default(['error', 'warn', 'migration']),
    maxQueryExecutionTime: z.coerce.number().int().positive().default(1000),
    dropSchema: bool.default(false),
    synchronize: bool.default(false),
    runMigrations: bool.default(true),
    defaultChunkSize: z.coerce.number().int().positive().default(100),
})

export const databaseSchema = base.extend({
    host: z.string().default('localhost'),
    port: z.coerce.number().int().positive().default(5432),
    username: z.string().default('postgres'),
    password: z.string().default('postgres'),
    database: z.string(),
    schema: z.string().default('public'),
})

export const database = z.union([z.string(), databaseSchema]).transform((val) => {
    return isString(val) ? databaseSchema.parse({ database: val }) : val
})
