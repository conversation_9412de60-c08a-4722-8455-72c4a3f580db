import { createConfig } from '../utils/config'
import { cleanup } from './cleanup'
import { database } from './database'
import { geyser } from './geyser'
import { logger } from './logger'
import { raydium } from './raydium'
import { rpc } from './rpc'
import { worker } from './worker'

export const config = createConfig({
    logger,
    database,
    rpc,
    geyser,
    worker,
    raydium,
    cleanup,
})
