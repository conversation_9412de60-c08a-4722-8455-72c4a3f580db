import { isExiting } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { formatNanoseconds } from '@kdt310722/utils/number'
import { config } from '../config'
import { Geyser } from '../modules/geyser/geyser'
import { RaydiumLaunchpad } from '../modules/raydium-launchpad/raydium-launchpad'
import { toSharedArrayBuffer } from '../modules/worker/utils/buffers'
import { createChildLogger } from './logger'
import { onIdle } from './slot'
import { workerPool } from './worker'

const logger = createChildLogger('core:geyser')

export const geyser = new Geyser(logger, config.geyser.url, RaydiumLaunchpad.getAccountFilter(), config.geyser)

geyser.on('data', (data) => {
    if (isExiting()) {
        return
    }

    workerPool.sendMessage(toSharedArrayBuffer(data))
})

geyser.on('unhandledData', (data) => logger.warn('Unhandled geyser data', data))

onIdle((time) => {
    geyser.unsubscribe(false, { error: new Error(`No geyser slot updates for ${formatNanoseconds(BigInt(time * 1e6))}`) }).catch((error) => {
        logger.exit(1, 'fatal', 'Failed to unsubscribe from geyser', error)
    })
})

export async function initializeGeyser() {
    const timer = tap(logger.createTimer(), () => logger.info('Connecting to geyser server...'))

    await geyser.subscribe().then(() => {
        logger.stopTimer(timer, 'info', 'Connected to geyser server!')
    })
}
