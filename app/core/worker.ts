import { highlight } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { isObject } from '@kdt310722/utils/object'
import { sleep } from '@kdt310722/utils/promise'
import { handleToken, handleTrade } from '../common/raydium'
import { config } from '../config'
import { WorkerPool } from '../modules/worker/pool'
import { appPath } from '../utils/path'
import { createChildLogger } from './logger'
import { slotManager } from './slot'

const logger = createChildLogger('core:worker')

export const workerPool = new WorkerPool(appPath('workers/worker.ts'), config.worker)

workerPool.on('worker:exit', (code, threadId) => {
    const message = `Worker ${highlight(`#${threadId}`)} exited with code ${highlight(code)}`

    if (code === 0) {
        logger.info(message)
    } else {
        logger.exit(code, 'fatal', message)
    }
})

workerPool.on('worker:error', (error, { worker }) => {
    logger.error(`Worker ${highlight(`#${worker.threadId}`)} error`, error)
})

slotManager.on('slot', (slot, data) => {
    workerPool.sendMessageToAllWorkers({ type: 'geyser:slot:update', slot, data })
})

workerPool.on('worker:unhandledMessage', (message, { worker }) => {
    if (isObject(message) && 'type' in message && 'data' in message) {
        if (message.type === 'geyser:slot:update') {
            return slotManager.handleSlotUpdate(message.data)
        }

        if (message.type === 'geyser:slot:blockMeta') {
            return slotManager.handleBlockMetaUpdate(message.data)
        }

        if (message.type === 'raydium:token') {
            return handleToken(message.data)
        }

        if (message.type === 'raydium:trade') {
            return handleTrade(message.data)
        }
    }

    logger.warn(`Unhandled worker message from worker ${highlight(`#${worker.threadId}`)}`, message)
})

export async function initializeWorkerPool() {
    const timer = tap(logger.createTimer(), () => logger.info('Initializing workers...'))

    await workerPool.init().then(() => sleep(500)).then(() => {
        logger.stopTimer(timer, 'info', 'All workers initialized!')
    })
}
