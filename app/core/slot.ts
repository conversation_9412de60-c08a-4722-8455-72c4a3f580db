import { isMainThread, threadId } from 'node:worker_threads'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { config } from '../config'
import { SlotManager } from '../modules/geyser/utils/slot-manager'
import { createChildLogger } from './logger'

export const slotManager = new SlotManager(config.geyser.slot)

const threadName = isMainThread ? 'main' : `worker:${threadId}`
const logger = createChildLogger(`core:slot:${threadName}`)
const idleHandlers = new Set<(time: number) => void>()

export function onIdle(handler: (time: number) => void) {
    idleHandlers.add(handler)
}

const gapHandlers = new Set<(startSlot: bigint, endSlot: bigint) => void>()

export function onGap(handler: (startSlot: bigint, endSlot: bigint) => void) {
    gapHandlers.add(handler)
}

let latestSlot: bigint | undefined

slotManager.on('slot', (slot) => {
    if (isNullish(latestSlot) || slot > latestSlot) {
        if (notNullish(latestSlot)) {
            const startSlot = latestSlot + 1n
            const endSlot = slot - 1n

            if (startSlot <= endSlot) {
                for (const handler of gapHandlers) {
                    handler(startSlot, endSlot)
                }
            }
        }

        logger.debug(`Current geyser slot: ${latestSlot = slot} (${threadName})`)
    }
})

slotManager.on('idle', (time) => {
    for (const handler of idleHandlers) {
        handler(time)
    }
})
