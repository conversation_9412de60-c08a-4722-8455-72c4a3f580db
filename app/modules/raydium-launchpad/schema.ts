import type { NonEmpty } from '@kdt310722/utils/array'
import { keys } from '@kdt310722/utils/object'
import { ConstantProductCurve, FixedPriceCurve, LinearPriceCurve } from '@kdt-bun/raydium-launchpad-sdk'
import z from 'zod'
import { address } from '../../utils/schemas/address'

export const RAYDIUM_CURVES = <const>{
    constant: ConstantProductCurve,
    fixed: FixedPriceCurve,
    linear: LinearPriceCurve,
}

export type RaydiumCurve = keyof typeof RAYDIUM_CURVES

const schema = z.object({
    curve: z.enum(keys(RAYDIUM_CURVES) as NonEmpty<RaydiumCurve>).transform((i) => RAYDIUM_CURVES[i]).default('constant'),
    globalConfig: address.default('6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX'),
    platformConfig: address.default('FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1'),
    quoteToken: address.default('So11111111111111111111111111111111111111112'),
    baseDecimals: z.number().int().positive().default(6),
    quoteDecimals: z.number().int().positive().default(9),
    priceDecimals: z.number().int().positive().default(18),
    allowUnknownGlobalConfig: z.boolean().default(false),
    allowUnknownPlatformConfig: z.boolean().default(true),
})

export const raydiumSchema = schema.default({})
