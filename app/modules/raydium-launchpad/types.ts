import type { Curve, ParsedBuyExactInInstruction } from '@kdt-bun/raydium-launchpad-sdk'
import type { Address } from '@solana/kit'
import type { Transaction as BaseTransaction } from '../rpc-client/types'

export type Transaction = BaseTransaction

export type TradeInstructionAccounts = ParsedBuyExactInInstruction['accounts']

export interface RaydiumLaunchpadConfig {
    curve: Curve
    globalConfig: Address
    platformConfig: Address
    quoteToken: Address
    baseDecimals: number
    quoteDecimals: number
    priceDecimals: number
    allowUnknownGlobalConfig: boolean
    allowUnknownPlatformConfig: boolean
}
