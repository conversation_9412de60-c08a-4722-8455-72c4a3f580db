import type { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTradesTable1754055354262 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "trades" ("signature" character varying NOT NULL, "slot" integer NOT NULL, "transaction_index" integer NOT NULL, "instruction_index" integer NOT NULL, "sort_key" character varying NOT NULL, "mint" character varying NOT NULL, "pool_id" character varying NOT NULL, "user" character varying NOT NULL, "is_buy" boolean NOT NULL, "accounts" text NOT NULL, "amount_in" character varying NOT NULL, "amount_out" character varying NOT NULL, "reserves" text NOT NULL, "price" character varying NOT NULL, "fees" text NOT NULL, "global_config" character varying NOT NULL, "platform_config" character varying NOT NULL, "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL, CONSTRAINT "UQ_35ad1fef76f83787b6e0f6a7021" UNIQUE ("sort_key"), CONSTRAINT "PK_4a1deb752167e852583f074462b" PRIMARY KEY ("signature", "instruction_index", "timestamp"))`);
        await queryRunner.query(`CREATE INDEX "IDX_0154d150458d0e0037d93f8c6a" ON "trades" ("slot") `);
        await queryRunner.query(`CREATE INDEX "IDX_b8974346b489ca92f262b885f4" ON "trades" ("transaction_index") `);
        await queryRunner.query(`CREATE INDEX "IDX_35ad1fef76f83787b6e0f6a702" ON "trades" ("sort_key") `);
        await queryRunner.query(`CREATE INDEX "IDX_6fd1eaa0588ce88667c034b096" ON "trades" ("mint") `);
        await queryRunner.query(`CREATE INDEX "IDX_28b0a6f2bcc14091948c560b46" ON "trades" ("pool_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_809c1f02f3df6af530ae12bf01" ON "trades" ("user") `);
        await queryRunner.query(`CREATE INDEX "IDX_a3b9ae52fdca48314bbd17d5ba" ON "trades" ("is_buy") `);
        await queryRunner.query(`CREATE INDEX "IDX_faf591a5b5349160d9aabf70fb" ON "trades" ("global_config") `);
        await queryRunner.query(`CREATE INDEX "IDX_8bed13de032cf3c4f55fea3350" ON "trades" ("platform_config") `);
        await queryRunner.query(`CREATE INDEX "IDX_09ebd8403ab7f15d19ed1ba525" ON "trades" ("timestamp") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_09ebd8403ab7f15d19ed1ba525"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8bed13de032cf3c4f55fea3350"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_faf591a5b5349160d9aabf70fb"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a3b9ae52fdca48314bbd17d5ba"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_809c1f02f3df6af530ae12bf01"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_28b0a6f2bcc14091948c560b46"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6fd1eaa0588ce88667c034b096"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_35ad1fef76f83787b6e0f6a702"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b8974346b489ca92f262b885f4"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_0154d150458d0e0037d93f8c6a"`);
        await queryRunner.query(`DROP TABLE "trades"`);
    }
}
