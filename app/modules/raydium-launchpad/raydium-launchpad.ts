import type { Address } from '@solana/kit'
import type { AccountFilter } from '../backfill/utils/accounts'
import type { DecodedTransactionInstruction } from '../rpc-client/types'
import type { RaydiumLaunchpadConfig, TradeInstructionAccounts, Transaction } from './types'
import { isNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import { parseBuyExactInInstruction, parseBuyExactOutInstruction, type ParsedBuyExactInInstruction, type ParsedBuyExactOutInstruction, parseInitializeInstruction, parseSellExactInInstruction, parseSellExactOutInstruction, RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS, RaydiumLaunchpadInstruction, type TradeEvent } from '@kdt-bun/raydium-launchpad-sdk'
import { type DataSource, LessThan, type Repository } from 'typeorm'
import { Token } from '../../entities/token'
import { chunkUpsert } from '../../utils/database/repositories'
import { TRADE_EVENT_DECODER } from './constants'
import { Trade } from './entities/trade'
import { toTokenEntity } from './utils/formatters/to-token-entity'
import { toTradeEntity } from './utils/formatters/to-trade-entity'
import { getInstructionType, isTradeEventInstruction } from './utils/instructions'

export type RaydiumLaunchpadEvents = {
    token: (token: Token) => void
    trade: (trade: Trade) => void
    unhandledInstruction: (instructionType: RaydiumLaunchpadInstruction, instruction: DecodedTransactionInstruction, transaction: Transaction) => void
    unhandledGlobalConfig: (address: Address, transaction: Transaction) => void
    unhandledPlatformConfig: (address: Address, transaction: Transaction) => void
    unhandledQuoteToken: (address: Address, transaction: Transaction) => void
}

export class RaydiumLaunchpad extends Emitter<RaydiumLaunchpadEvents, true> {
    public readonly tokenRepository: Repository<Token>
    public readonly tradeRepository: Repository<Trade>

    public constructor(protected readonly database: DataSource, public readonly config: RaydiumLaunchpadConfig) {
        super()

        this.tokenRepository = database.getRepository(Token)
        this.tradeRepository = database.getRepository(Trade)
    }

    public static getAccountFilter(): AccountFilter {
        return { required: [RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS] }
    }

    public async getLastKnownSlot() {
        const tokenSlot = await this.tokenRepository.findOne({ order: { createdAtSlot: 'DESC' } }).then((token) => token?.createdAtSlot)
        const tradeSlot = await this.tradeRepository.findOne({ order: { slot: 'DESC' } }).then((trade) => trade?.slot)

        if (isNullish(tokenSlot) && isNullish(tradeSlot)) {
            return
        }

        return BigInt(Math.max(tokenSlot ?? -1, tradeSlot ?? -1))
    }

    public async cleanup(maxTokenAge: number) {
        const { maxQueryExecutionTime } = this.database.options

        this.database.setOptions({
            maxQueryExecutionTime: undefined,
        })

        const tokensDeleteResult = await this.tokenRepository.createQueryBuilder().delete().where({ createdAt: LessThan(new Date(Date.now() - maxTokenAge)) }).execute()
        const tokensQuery = this.tokenRepository.createQueryBuilder('token').select('token.mint', 'mint').getQuery()
        const tradesDeleteResult = await this.tradeRepository.createQueryBuilder().delete().where(`"trades"."mint" NOT IN (${tokensQuery})`).execute()

        return tap({ tokens: tokensDeleteResult.affected, trades: tradesDeleteResult.affected }, () => {
            this.database.setOptions({ maxQueryExecutionTime })
        })
    }

    public async handleTransactions(transactions: Transaction[]) {
        const tokens: Token[] = []
        const trades: Trade[] = []

        for (const transaction of transactions) {
            try {
                const { tokens: transactionTokens, trades: transactionTrades } = this.handleTransaction(transaction)

                tokens.push(...transactionTokens)
                trades.push(...transactionTrades)
            } catch (error) {
                if (error instanceof Error) {
                    throw Object.assign(error, { transaction: transaction.signature })
                }

                throw error
            }
        }

        await chunkUpsert(this.tokenRepository, tokens, ['mint'])
        await chunkUpsert(this.tradeRepository, trades, ['signature', 'instruction_index', 'timestamp'])
    }

    protected handleTransaction(transaction: Transaction) {
        const tradeEventIndexes: number[] = []
        const tradeEvents = transaction.instructions.filter((instruction, i) => tap(isTradeEventInstruction(instruction), (isValid) => isValid && tradeEventIndexes.push(i))).map((i) => TRADE_EVENT_DECODER.decode(i.data.subarray(16)))

        const tokens: Token[] = []
        const trades: Trade[] = []

        for (const [index, instruction] of transaction.instructions.entries()) {
            if (tradeEventIndexes.includes(index) || instruction.programAddress !== RAYDIUM_LAUNCHPAD_PROGRAM_ADDRESS) {
                continue
            }

            const entity = this.handleInstruction(index, instruction, transaction, tradeEvents)

            if (entity instanceof Token) {
                tokens.push(entity)
            } else if (entity instanceof Trade) {
                trades.push(entity)
            }
        }

        return { tokens, trades }
    }

    protected handleInstruction(index: number, instruction: DecodedTransactionInstruction, transaction: Transaction, tradeEvents: TradeEvent[]) {
        const instructionType = getInstructionType(instruction)

        if (isNullish(instructionType)) {
            return
        }

        switch (instructionType) {
            case RaydiumLaunchpadInstruction.Initialize:
                return this.handleInitializeInstruction(instruction, transaction)
            case RaydiumLaunchpadInstruction.BuyExactIn:
                return this.handleExactInTradeInstruction(parseBuyExactInInstruction(instruction), tradeEvents, transaction, index)
            case RaydiumLaunchpadInstruction.BuyExactOut:
                return this.handleExactOutTradeInstruction(parseBuyExactOutInstruction(instruction), tradeEvents, transaction, index)
            case RaydiumLaunchpadInstruction.SellExactIn:
                return this.handleExactInTradeInstruction(parseSellExactInInstruction(instruction), tradeEvents, transaction, index)
            case RaydiumLaunchpadInstruction.SellExactOut:
                return this.handleExactOutTradeInstruction(parseSellExactOutInstruction(instruction), tradeEvents, transaction, index)
            case RaydiumLaunchpadInstruction.ClaimPlatformFee:
            case RaydiumLaunchpadInstruction.ClaimVestedToken:
            case RaydiumLaunchpadInstruction.CollectFee:
            case RaydiumLaunchpadInstruction.CollectMigrateFee:
            case RaydiumLaunchpadInstruction.CreateVestingAccount:
            case RaydiumLaunchpadInstruction.MigrateToAmm:
            case RaydiumLaunchpadInstruction.MigrateToCpswap:
                break
            default:
                this.emit('unhandledInstruction', instructionType, instruction, transaction)
        }

        return void 0
    }

    protected handleExactInTradeInstruction(parsedInstruction: ParsedBuyExactInInstruction<string>, tradeEvents: TradeEvent[], transaction: Transaction, index: number) {
        return this.handleTradeInstruction(parsedInstruction.accounts, tradeEvents, transaction, index)
    }

    protected handleExactOutTradeInstruction(parsedInstruction: ParsedBuyExactOutInstruction<string>, tradeEvents: TradeEvent[], transaction: Transaction, index: number) {
        return this.handleTradeInstruction(parsedInstruction.accounts, tradeEvents, transaction, index)
    }

    protected handleTradeInstruction(accounts: TradeInstructionAccounts, tradeEvents: TradeEvent[], transaction: Transaction, instructionIndex: number) {
        const tradeEvent = tradeEvents.shift()

        if (isNullish(tradeEvent)) {
            throw new Error('No trade event found')
        }

        if (!this.config.allowUnknownGlobalConfig && accounts.globalConfig.address !== this.config.globalConfig) {
            return tap(void 0, () => this.emit('unhandledGlobalConfig', accounts.globalConfig.address, transaction))
        }

        if (!this.config.allowUnknownPlatformConfig && accounts.platformConfig.address !== this.config.platformConfig) {
            return tap(void 0, () => this.emit('unhandledPlatformConfig', accounts.platformConfig.address, transaction))
        }

        if (accounts.quoteTokenMint.address !== this.config.quoteToken) {
            return tap(void 0, () => this.emit('unhandledQuoteToken', accounts.quoteTokenMint.address, transaction))
        }

        return tap(toTradeEntity({ transaction, instructionIndex, tradeEvent, accounts, config: this.config }), (trade) => {
            this.emit('trade', trade)
        })
    }

    protected handleInitializeInstruction(instruction: DecodedTransactionInstruction, transaction: Transaction) {
        const data = parseInitializeInstruction(instruction)

        if (data.accounts.quoteMint.address !== this.config.quoteToken) {
            return tap(void 0, () => this.emit('unhandledQuoteToken', data.accounts.quoteMint.address, transaction))
        }

        return tap(toTokenEntity(data, transaction), (token) => {
            this.emit('token', token)
        })
    }
}
