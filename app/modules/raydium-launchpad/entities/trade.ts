import type { PoolReserve } from '@kdt-bun/raydium-launchpad-sdk'
import type { Address } from '@solana/kit'
import { Column, Entity, Index, PrimaryColumn } from 'typeorm'
import { BigIntColumn } from '../../../utils/database/columns/bigint-column'
import { JsonColumn } from '../../../utils/database/columns/json-column'
import { SolanaAddressColumn } from '../../../utils/database/columns/solana-address-column'

export interface TradeAccounts {
    baseVault: Address
    quoteVault: Address
}

export interface PoolFees {
    protocolFee: bigint
    platformFee: bigint
    shareFee: bigint
}

@Entity()
export class Trade {
    @PrimaryColumn()
    public declare signature: string

    @Index()
    @Column()
    public declare slot: number

    @Index()
    @Column()
    public declare transactionIndex: number

    @PrimaryColumn()
    public declare instructionIndex: number

    @Index()
    @BigIntColumn({ unique: true })
    public declare sortKey: bigint

    @Index()
    @SolanaAddressColumn()
    public declare mint: Address

    @Index()
    @SolanaAddressColumn()
    public declare poolId: Address

    @Index()
    @SolanaAddressColumn()
    public declare user: Address

    @Index()
    @Column()
    public declare isBuy: boolean

    @JsonColumn()
    public declare accounts: TradeAccounts

    @BigIntColumn()
    public declare amountIn: bigint

    @BigIntColumn()
    public declare amountOut: bigint

    @JsonColumn()
    public declare reserves: PoolReserve

    @BigIntColumn()
    public declare price: bigint

    @JsonColumn()
    public declare fees: PoolFees

    @Index()
    @SolanaAddressColumn()
    public declare globalConfig: Address

    @Index()
    @SolanaAddressColumn()
    public declare platformConfig: Address

    @Index()
    @PrimaryColumn({ type: 'timestamptz' })
    public declare timestamp: Date
}
