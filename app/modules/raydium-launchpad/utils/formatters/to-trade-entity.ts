import type { RaydiumLaunchpadConfig, TradeInstructionAccounts, Transaction } from '../../types'
import { tap } from '@kdt310722/utils/function'
import { type PoolReserve, TradeDirection, type TradeEvent } from '@kdt-bun/raydium-launchpad-sdk'
import { type PoolFees, Trade, type TradeAccounts } from '../../entities/trade'

export function getSortKey(transaction: Transaction, instructionIndex: number) {
    return (transaction.slot * 100_000_000n) + (BigInt(transaction.index) * 1000n) + BigInt(instructionIndex)
}

export const toTradeAccounts = (accounts: TradeInstructionAccounts): TradeAccounts => ({
    baseVault: accounts.baseVault.address,
    quoteVault: accounts.quoteVault.address,
})

export interface ToTradeEntityParams {
    transaction: Transaction
    instructionIndex: number
    tradeEvent: TradeEvent
    accounts: TradeInstructionAccounts
    config: Pick<RaydiumLaunchpadConfig, 'curve' | 'baseDecimals' | 'quoteDecimals' | 'priceDecimals'>
}

export const toTradeEntity = ({ transaction, instructionIndex, tradeEvent, accounts, config }: ToTradeEntityParams) => tap(new Trade(), (trade) => {
    const reserves: PoolReserve = {
        virtualBase: tradeEvent.virtualBase,
        virtualQuote: tradeEvent.virtualQuote,
        realBase: tradeEvent.realBaseAfter,
        realQuote: tradeEvent.realQuoteAfter,
    }

    const fees: PoolFees = {
        protocolFee: tradeEvent.protocolFee,
        platformFee: tradeEvent.platformFee,
        shareFee: tradeEvent.shareFee,
    }

    trade.signature = transaction.signature
    trade.slot = Number(transaction.slot)
    trade.transactionIndex = transaction.index
    trade.instructionIndex = instructionIndex
    trade.sortKey = getSortKey(transaction, instructionIndex)
    trade.mint = accounts.baseTokenMint.address
    trade.poolId = accounts.poolState.address
    trade.user = accounts.payer.address
    trade.isBuy = tradeEvent.tradeDirection === TradeDirection.Buy
    trade.accounts = toTradeAccounts(accounts)
    trade.amountIn = tradeEvent.amountIn
    trade.amountOut = tradeEvent.amountOut
    trade.reserves = reserves
    trade.price = BigInt(config.curve.calculatePoolPrice(reserves, config.baseDecimals, config.quoteDecimals).mul(10 ** config.priceDecimals).trunc().toString())
    trade.fees = fees
    trade.globalConfig = accounts.globalConfig.address
    trade.platformConfig = accounts.platformConfig.address
    trade.timestamp = transaction.blockTime ? new Date(Number(transaction.blockTime) * 1000) : new Date()
})
