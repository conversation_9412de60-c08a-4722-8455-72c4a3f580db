import type { AccountMeta, Address, Rpc, Signature, SolanaRpcApiMainnet, UnixTimestamp } from '@solana/kit'
import type { getTransaction } from './utils/transactions'

export type RpcHttpClient = Rpc<SolanaRpcApiMainnet>

export type SolanaTransaction = NonNullable<Awaited<ReturnType<typeof getTransaction>>>

export type SolanaTransactionInstruction = SolanaTransaction['transaction']['message']['instructions'][number]

export interface DecodedTransactionInstruction {
    programAddress: Address
    accounts: AccountMeta[]
    data: Buffer
}

export interface Transaction {
    slot: bigint
    index: number
    signature: Signature
    blockTime?: UnixTimestamp | null
    instructions: DecodedTransactionInstruction[]
}
