import type { Fn } from '@kdt310722/utils/function'
import type { WorkerTaskResultMessage } from './types/messages'
import type { TaskStats } from './types/tasks'
import { availableParallelism } from 'node:os'
import { type Transferable, Worker } from 'node:worker_threads'
import { createAbortError } from '@kdt310722/utils/error'
import { Emitter } from '@kdt310722/utils/event'
import { createDeferredWithTimeout, type DeferredPromise, pTap } from '@kdt310722/utils/promise'
import { deserializeError } from 'serialize-error'
import { WORKER_EXEC_ARGV, WorkerMessageType } from './constants'
import { isAbortMessage, isPingMessage, isShutdownFailedMessage, isStartedMessage, isStartFailedMessage, isTaskResultMessage, isWorkerMessage } from './utils/messages'

export interface WorkerPoolTimeoutOptions {
    start?: number
    task?: number
    shutdown?: number
}

export interface WorkerPoolOptions {
    size?: number
    timeout?: WorkerPoolTimeoutOptions
}

export interface WorkerEventContext {
    worker: Worker
}

export interface TaskEventContext extends WorkerEventContext {
    stats: TaskStats
}

export type WorkerPoolEvents = {
    'worker:start': () => void
    'worker:started': (context: WorkerEventContext) => void
    'worker:exit': (code: number, threadId: number, context: WorkerEventContext) => void
    'worker:error': (error: unknown, context: WorkerEventContext) => void
    'worker:unhandledMessage': (message: unknown, context: WorkerEventContext) => void
    'worker:task:result': (taskId: string, result: unknown | undefined, context: TaskEventContext) => void
    'worker:task:failure': (taskId: string, error: unknown, context: TaskEventContext) => void
}

export class WorkerPool extends Emitter<WorkerPoolEvents, true> {
    public isStarting = false
    public isStarted = false
    public isShuttingDown = false

    protected readonly size_: number
    protected readonly startTimeout: number
    protected readonly taskTimeout: number
    protected readonly shutdownTimeout: number

    protected readonly workers: Record<number, Worker> = {}
    protected readonly tasks: Record<string, DeferredPromise<unknown>> = {}
    protected readonly taskWorkerThreadIds: Record<string, number> = {}

    protected readonly eventHandlers: Record<number, Record<string, Fn>> = {}
    protected readonly shutdownPromises: Record<number, DeferredPromise<void>> = {}

    protected workerThreadIds: number[] = []
    protected currentWorkerIndex = 0
    protected taskId = 0n

    public constructor(protected readonly path: string, { size = availableParallelism(), timeout = {} }: WorkerPoolOptions = {}) {
        super()

        this.size_ = size
        this.startTimeout = timeout.start ?? 10_000
        this.taskTimeout = timeout.task ?? 30_000
        this.shutdownTimeout = timeout.shutdown ?? 10_000
    }

    public get size() {
        return this.workerThreadIds.length
    }

    public async shutdown(terminateOnFailure?: boolean) {
        if (this.isShuttingDown) {
            throw new Error('Worker pool already shutting down')
        }

        if (!this.isStarted) {
            return
        }

        this.isShuttingDown = true
        this.isStarted = false

        try {
            for (const worker of Object.values(this.workers)) {
                await this.shutdownWorker(worker, terminateOnFailure)
            }
        } finally {
            this.isShuttingDown = false
        }
    }

    public async submitTaskAndWait<TRequest = unknown, TResult = unknown>(data: TRequest) {
        const id = String(++this.taskId)
        const timeoutMessage = `Task ${id} timed out`
        const promise = this.tasks[id] = createDeferredWithTimeout<unknown>(this.taskTimeout, timeoutMessage)

        try {
            return await Promise.resolve(this.submitTask(data, id)).then(() => promise) as TResult
        } catch (error) {
            if (error instanceof Error && error.message === timeoutMessage) {
                this.abort(id)
            }

            throw error
        } finally {
            delete this.tasks[id]
        }
    }

    public abort(taskId: string, fromWorker = false) {
        const workerThreadId = this.taskWorkerThreadIds[taskId]
        const worker = this.workers[workerThreadId]

        if (!worker) {
            return
        }

        this.tasks[taskId]?.reject(createAbortError(`Task ${taskId} aborted`))

        if (!fromWorker) {
            worker.postMessage({ type: WorkerMessageType.Abort, payload: taskId })
        }

        delete this.taskWorkerThreadIds[taskId]
    }

    public submitTask<T>(data: T, taskId?: string) {
        const id = taskId ?? String(++this.taskId)
        const workerThreadId = this.sendMessage({ type: WorkerMessageType.Task, payload: { id, data, submitTime: process.hrtime.bigint() } })

        this.taskWorkerThreadIds[id] = workerThreadId

        return id
    }

    public sendMessage(message: unknown, transferList?: readonly Transferable[]) {
        if (!this.isStarted) {
            throw new Error('Worker pool not started')
        }

        const workerThreadId = this.workerThreadIds[this.currentWorkerIndex++ % this.workerThreadIds.length]
        const worker = this.workers[workerThreadId]

        if (!worker) {
            throw new Error('No available workers')
        }

        worker.postMessage(message, transferList)

        return workerThreadId
    }

    public sendMessageToWorker(threadId: number, message: unknown, transferList?: readonly Transferable[]) {
        this.workers[threadId]?.postMessage(message, transferList)
    }

    public sendMessageToAllWorkers(message: unknown, exclude: number[] = []) {
        for (const [threadId, worker] of Object.entries(this.workers)) {
            if (exclude.includes(Number(threadId))) {
                continue
            }

            worker.postMessage(message)
        }
    }

    public async init() {
        if (this.isStarting || this.isStarted) {
            return
        }

        this.isStarting = true

        try {
            for (let i = 0; i < this.size_; i++) {
                const worker = await this.createWorker()

                this.workerThreadIds.push(worker.threadId)
                this.workers[worker.threadId] = worker
            }

            this.isStarted = true
        } finally {
            this.isStarting = false
        }
    }

    protected async shutdownWorker(worker: Worker, terminateOnFailure = true) {
        if (!this.workers[worker.threadId]) {
            throw new Error('Worker not found')
        }

        this.workerThreadIds = this.workerThreadIds.filter((id) => id !== worker.threadId)
        this.shutdownPromises[worker.threadId] = createDeferredWithTimeout<void>(this.shutdownTimeout, `Worker #${worker.threadId} shutdown timed out`)

        worker.postMessage({ type: WorkerMessageType.Shutdown })

        try {
            await this.shutdownPromises[worker.threadId]
        } catch (error) {
            const errors: unknown[] = [error]

            if (terminateOnFailure) {
                await Promise.resolve(this.resetWorker(worker.threadId, worker)).then(() => worker.terminate()).catch((error) => errors.push(error))
            }

            throw errors.length > 1 ? new AggregateError(errors, 'Failed to shutdown worker') : error
        } finally {
            delete this.shutdownPromises[worker.threadId]
        }
    }

    protected async createWorker(): Promise<Worker> {
        this.emit('worker:start')

        const started = createDeferredWithTimeout<void>(this.startTimeout, 'Worker start timed out')
        const worker = new Worker(this.path, { execArgv: WORKER_EXEC_ARGV, argv: process.argv })
        const threadId = worker.threadId

        this.eventHandlers[threadId] = {}

        worker.on('error', this.eventHandlers[threadId]['error'] = this.handleError.bind(this, started, worker))
        worker.on('messageerror', this.eventHandlers[threadId]['messageerror'] = this.handleError.bind(this, started, worker))
        worker.on('exit', this.eventHandlers[threadId]['exit'] = this.handleExit.bind(this, started, worker, threadId))
        worker.on('message', this.eventHandlers[threadId]['message'] = this.handleMessage.bind(this, started, worker))

        return started.then(() => worker).then(pTap((worker) => this.emit('worker:started', { worker }))).catch(pTap.catch(async () => {
            await Promise.resolve(this.resetWorker(threadId, worker)).then(() => worker.terminate())
        }))
    }

    protected resetWorker(threadId: number, worker: Worker) {
        this.workerThreadIds = this.workerThreadIds.filter((id) => id !== threadId)

        if (this.eventHandlers[threadId]) {
            for (const [event, handler] of Object.entries(this.eventHandlers[threadId])) {
                worker.off(event, handler)
            }
        }

        for (const [taskId, workerThreadId] of Object.entries(this.taskWorkerThreadIds)) {
            if (workerThreadId === threadId) {
                this.abort(taskId, true)
            }
        }

        delete this.eventHandlers[threadId]
        delete this.workers[threadId]
    }

    protected handleError(promise: DeferredPromise<void>, worker: Worker, error: unknown) {
        if (!promise.isSettled) {
            return promise.reject(error)
        }

        this.emit('worker:error', error, { worker })
    }

    protected handleExit(promise: DeferredPromise<void>, worker: Worker, threadId: number, code: number) {
        if (!promise.isSettled) {
            return promise.reject(new Error(`Worker #${threadId} exited unexpectedly with code ${code}`))
        }

        this.resetWorker(threadId, worker)
        this.shutdownPromises[threadId]?.resolve()
        this.emit('worker:exit', code, threadId, { worker })
    }

    protected handleMessage(promise: DeferredPromise<void>, worker: Worker, message: unknown) {
        if (!isWorkerMessage(message)) {
            if (!promise.isSettled) {
                return promise.reject(Object.assign(new Error(`Worker #${worker.threadId} sent an invalid message`), { message }))
            }

            return this.emit('worker:unhandledMessage', message, { worker })
        }

        if (!promise.isSettled) {
            if (isStartedMessage(message)) {
                return promise.resolve()
            }

            if (isStartFailedMessage(message)) {
                return promise.reject(deserializeError(message.error))
            }

            return promise.reject(Object.assign(new Error(`Worker #${worker.threadId} sent an invalid message`), { message }))
        }

        if (isTaskResultMessage(message)) {
            return this.handleTaskResult(worker, message)
        }

        if (isPingMessage(message)) {
            return worker.postMessage({ type: WorkerMessageType.Pong })
        }

        if (isAbortMessage(message)) {
            return this.abort(message.payload, true)
        }

        if (isShutdownFailedMessage(message)) {
            return this.shutdownPromises[worker.threadId]?.reject(deserializeError(message.error))
        }

        this.emit('worker:unhandledMessage', message, { worker })
    }

    protected handleTaskResult(worker: Worker, message: WorkerTaskResultMessage) {
        const taskId = message.payload.id

        const stats: TaskStats = {
            resultReceivedTime: process.hrtime.bigint() - message.payload.submitTime,
            executionTime: message.payload.executionTime,
            receivedTime: message.payload.receivedTime,
        }

        delete this.taskWorkerThreadIds[taskId]

        if (message.payload.isSuccess) {
            this.tasks[taskId]?.resolve(message.payload.result)
            this.emit('worker:task:result', taskId, message.payload.result, { stats, worker })
        } else {
            const error = deserializeError(message.payload.error)

            this.tasks[taskId]?.reject(error)
            this.emit('worker:task:failure', taskId, error, { stats, worker })
        }
    }
}
