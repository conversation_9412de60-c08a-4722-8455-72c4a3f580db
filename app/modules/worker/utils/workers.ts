import type { Awaitable } from '@kdt310722/utils/promise'
import type { WorkerMessage } from '../types/messages'
import type { Task } from '../types/tasks'
import { parentPort } from 'node:worker_threads'
import { isAbortError } from '@kdt310722/utils/error'
import { isNumber } from '@kdt310722/utils/number'
import { serializeError } from 'serialize-error'
import { WorkerMessageType } from '../constants'
import { isAbortMessage, isPingMessage, isShutdownMessage, isTaskMessage, isWorkerMessage } from './messages'

export function sendRawMessage(message: unknown) {
    parentPort?.postMessage(message)
}

export function sendMessage(message: WorkerMessage) {
    sendRawMessage(message)
}

export function exit(error?: unknown): never {
    if (error) {
        if (error instanceof Error && 'exitCode' in error && isNumber(error.exitCode)) {
            process.exit(error.exitCode)
        } else {
            process.exit(1)
        }
    }

    process.exit(0)
}

const exitTasks = new Set<() => Awaitable<void>>()

export function addExitTask(task: () => Awaitable<void>) {
    exitTasks.add(task)
}

export function removeExitTask(task: () => Awaitable<void>) {
    exitTasks.delete(task)
}

let isExiting_ = false
let maxWaitTime_ = 5000

export function isExiting() {
    return isExiting_
}

export function setMaxWaitTime(maxWaitTime: number) {
    maxWaitTime_ = maxWaitTime
}

const abortControllers: Record<string, AbortController> = {}

export function gracefulExit() {
    if (isExiting_) {
        return
    }

    isExiting_ = true

    for (const abortController of Object.values(abortControllers)) {
        abortController.abort()
    }

    Promise.all([...exitTasks].map((task) => task())).then(() => exit()).catch((error: unknown) => {
        sendMessage({ type: WorkerMessageType.ShutdownFailed, error: serializeError(error) })
        exit(error)
    })

    setTimeout(() => exit(), maxWaitTime_)
}

export type TaskHandler<T = unknown, TResult = unknown> = (data: T, signal: AbortSignal) => Awaitable<TResult>

function handleTask(task: Task, handler: TaskHandler, receivedAt: bigint) {
    const start = process.hrtime.bigint()
    const abortController = abortControllers[task.id] = new AbortController()

    const sendResult = (result: { isSuccess: true, result?: unknown } | { isSuccess: false, error: unknown }) => sendMessage({
        type: WorkerMessageType.TaskResult,
        payload: { executionTime: process.hrtime.bigint() - start, id: task.id, receivedTime: receivedAt, ...result, submitTime: process.hrtime.bigint() },
    })

    const handleError = (error: unknown) => {
        if (isAbortError(error)) {
            return
        }

        sendResult({ isSuccess: false, error: serializeError(error) })
    }

    Promise.resolve(handler(task.data, abortController.signal)).then((r) => sendResult({ isSuccess: true, result: r })).catch(handleError).finally(() => {
        delete abortControllers[task.id]
    })
}

export interface InitializeWorkerOptions {
    onStarted?: () => Awaitable<void>
    onStartFailed?: (error: unknown) => Awaitable<void>
    onUnhandledMessage?: (message: unknown) => void
}

export function initializeWorker(fn: () => Awaitable<void>, taskHandler: TaskHandler, { onStarted, onStartFailed, onUnhandledMessage }: InitializeWorkerOptions = {}) {
    const handleSuccess = async () => {
        parentPort?.on('message', (message) => {
            const receivedAt = process.hrtime.bigint()

            if (isExiting_) {
                return
            }

            if (isWorkerMessage(message)) {
                if (isTaskMessage(message)) {
                    return handleTask(message.payload, taskHandler, receivedAt)
                }

                if (isAbortMessage(message)) {
                    return abortControllers[message.payload]?.abort()
                }

                if (isPingMessage(message)) {
                    return sendMessage({ type: WorkerMessageType.Pong })
                }

                if (isShutdownMessage(message)) {
                    return gracefulExit()
                }
            }

            onUnhandledMessage?.(message)
        })

        await onStarted?.()
        sendMessage({ type: WorkerMessageType.Started })
    }

    Promise.resolve(fn()).then(handleSuccess).catch(async (error) => {
        await onStartFailed?.(error)
        sendMessage({ type: WorkerMessageType.StartFailed, error: serializeError(error) })
        exit(error)
    })
}
