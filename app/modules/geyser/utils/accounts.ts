import type { SubscribeUpdateTransactionInfo } from '@kdt-farm/solana-grpc-client/yellowstone'
import { notNullish } from '@kdt310722/utils/common'
import { type AccountMeta, AccountRole, address } from '@solana/kit'
import { getAccountRole } from '../../rpc-client/utils/accounts'
import { BASE58_CODEC } from './formatters/to-transaction'

export function getTransactionAccounts({ transaction, meta }: SubscribeUpdateTransactionInfo): AccountMeta[] {
    if (!transaction?.message?.header) {
        throw new Error('Transaction message header is missing')
    }

    const accountKeys = transaction.message.accountKeys
    const header = transaction.message.header
    const loadedAddresses = { writable: meta?.loadedWritableAddresses ?? [], readonly: meta?.loadedReadonlyAddresses ?? [] }

    const accounts = accountKeys.map((account, index): AccountMeta => {
        const isWritable = index < header.numRequiredSignatures - header.numReadonlySignedAccounts || (index >= header.numRequiredSignatures && index < accountKeys.length - header.numReadonlyUnsignedAccounts)
        const isSigner = index < header.numRequiredSignatures

        return { address: address(BASE58_CODEC.decode(account)), role: getAccountRole(isWritable, isSigner) }
    })

    if (notNullish(loadedAddresses)) {
        accounts.push(
            ...loadedAddresses.writable.map((i) => ({ address: address(BASE58_CODEC.decode(i)), role: AccountRole.WRITABLE })),
            ...loadedAddresses.readonly.map((i) => ({ address: address(BASE58_CODEC.decode(i)), role: AccountRole.READONLY })),
        )
    }

    return accounts
}
