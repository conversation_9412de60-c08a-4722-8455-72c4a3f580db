import type { SubscribeUpdateTransactionInfo } from '@kdt-farm/solana-grpc-client/yellowstone'
import type { Transaction } from '../../../rpc-client/types'
import { getBase58Codec, signature } from '@solana/kit'
import { getTransactionInstructions } from '../instructions'

export const BASE58_CODEC = getBase58Codec()

export const toTransaction = (slot: bigint, data: SubscribeUpdateTransactionInfo): Transaction => ({
    slot,
    index: Number(data.index),
    signature: signature(BASE58_CODEC.decode(data.signature)),
    instructions: getTransactionInstructions(data),
})
