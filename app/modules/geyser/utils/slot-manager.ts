import type { SlotStatus, SubscribeUpdateBlockMeta, SubscribeUpdateSlot } from '@kdt-farm/solana-grpc-client/yellowstone'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { transform } from '@kdt310722/utils/function'
import { LruMap } from '@kdt310722/utils/object'
import { createDeferredWithTimeout, type DeferredPromise } from '@kdt310722/utils/promise'

export interface GeyserSlot {
    slot: bigint
    status: SlotStatus
    blockMetaUpdated: boolean
    blockTime?: bigint
}

export type SlotManagerEvents = {
    slot: (slot: bigint, data: Partial<GeyserSlot>) => void
    idle: (time: number) => void
}

export interface SlotManagerOptions {
    maxSlots?: number
    waitTimeout?: number
    idleTimeout?: number
}

export class SlotManager extends Emitter<SlotManagerEvents, true> {
    public firstFullSlot?: GeyserSlot
    public latestSlot?: bigint
    public lastSlotReceivedAt?: number
    public latestBlockMetaSlot?: bigint

    protected readonly slots: LruMap<string, Partial<GeyserSlot>>
    protected readonly waitTimeout: number
    protected readonly idleTimeout: number

    protected readonly slotStatusWaiters: Record<string, Set<{ status: SlotStatus, promise: DeferredPromise<SlotStatus> }>> = {}
    protected readonly slotTimestampWaiters: Record<string, Set<DeferredPromise<bigint | undefined>>> = {}

    protected idleTimer?: NodeJS.Timeout

    public constructor({ maxSlots = 10_000, waitTimeout = 60 * 1000, idleTimeout = 5000 }: SlotManagerOptions = {}) {
        super()

        this.slots = new LruMap(maxSlots)
        this.waitTimeout = waitTimeout
        this.idleTimeout = idleTimeout
    }

    public get(slot: bigint) {
        return transform(this.slots.get(slot.toString()), (data) => (notNullish(data?.slot) && notNullish(data.status) && data.blockMetaUpdated ? data as GeyserSlot : undefined))
    }

    public async waitForStatus(slot: bigint, status: SlotStatus, timeout = this.waitTimeout) {
        const existing = this.get(slot)

        if (isNullish(existing)) {
            if (notNullish(this.latestSlot) && slot < this.latestSlot) {
                return
            }
        } else if (existing.status >= status) {
            return existing.status
        }

        const key = slot.toString()
        const waiters = this.slotStatusWaiters[key] ??= new Set()

        const item = {
            status,
            promise: createDeferredWithTimeout<SlotStatus>(timeout, () => new Error(`Timeout waiting for slot ${slot} to reach status ${status}`)),
        }

        waiters.add(item)

        return item.promise.finally(() => {
            waiters.delete(item)

            if (waiters.size === 0) {
                delete this.slotStatusWaiters[key]
            }
        })
    }

    public async waitForTimestamp(slot: bigint, timeout = this.waitTimeout) {
        const existing = this.get(slot)

        if (isNullish(existing)) {
            if (notNullish(this.latestBlockMetaSlot) && slot < this.latestBlockMetaSlot) {
                return
            }
        } else {
            return existing.blockTime
        }

        const key = slot.toString()
        const waiters = this.slotTimestampWaiters[key] ??= new Set()
        const promise = createDeferredWithTimeout<bigint | undefined>(timeout, () => new Error(`Timeout waiting for slot ${slot} to have timestamp`))

        waiters.add(promise)

        return promise.finally(() => {
            waiters.delete(promise)

            if (waiters.size === 0) {
                delete this.slotTimestampWaiters[key]
            }
        })
    }

    public handleSlotUpdate(data: SubscribeUpdateSlot) {
        this.updateSlot(data.slot, { slot: data.slot, status: data.status })
    }

    public handleBlockMetaUpdate(data: SubscribeUpdateBlockMeta) {
        this.updateSlot(data.slot, { blockMetaUpdated: true, blockTime: data.blockTime?.timestamp })
    }

    public updateSlot(slot: bigint, data: Partial<GeyserSlot>) {
        this.lastSlotReceivedAt = Date.now()

        clearTimeout(this.idleTimer)

        const key = slot.toString()
        const current = this.slots.get(key)

        this.slots.set(key, { blockMetaUpdated: false, ...current, ...data })
        this.emit('slot', slot, data)
        this.idleTimer = setTimeout(() => this.emit('idle', this.idleTimeout), this.idleTimeout)

        if (isNullish(this.firstFullSlot)) {
            this.firstFullSlot = this.get(slot)
        }

        if (notNullish(data.status)) {
            if (isNullish(this.latestSlot) || slot > this.latestSlot) {
                this.latestSlot = slot
            }

            this.resolveStatusWaiters(slot, data.status)
        }

        if (data.blockMetaUpdated) {
            if (isNullish(this.latestBlockMetaSlot) || slot > this.latestBlockMetaSlot) {
                this.latestBlockMetaSlot = slot
            }

            this.resolveTimestampWaiters(slot, data.blockTime)
        }
    }

    protected resolveStatusWaiters(slot: bigint, status: SlotStatus) {
        const waiters = this.slotStatusWaiters[slot.toString()]

        if (waiters?.size) {
            for (const waiter of waiters) {
                if (status >= waiter.status) {
                    waiter.promise.resolve(status)
                }
            }
        }
    }

    protected resolveTimestampWaiters(slot: bigint, blockTime?: bigint) {
        const waiters = this.slotTimestampWaiters[slot.toString()]

        if (waiters?.size) {
            for (const waiter of waiters) {
                waiter.resolve(blockTime)
            }
        }
    }
}
