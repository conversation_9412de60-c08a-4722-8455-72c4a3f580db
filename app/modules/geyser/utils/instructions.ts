import type { CompiledInstruction, SubscribeUpdateTransactionInfo } from '@kdt-farm/solana-grpc-client/yellowstone'
import type { AccountMeta } from '@solana/kit'
import type { DecodedTransactionInstruction } from '../../rpc-client/types'
import { getTransactionAccounts } from './accounts'

export const decodeTransactionInstruction = (accountKeys: AccountMeta[], instruction: CompiledInstruction) => ({
    programAddress: accountKeys[instruction.programIdIndex].address,
    accounts: [...instruction.accounts].map((i) => accountKeys[i]),
    data: instruction.data,
})

export function getTransactionInstructions(data: SubscribeUpdateTransactionInfo) {
    const accountKeys = getTransactionAccounts(data)
    const instructions: DecodedTransactionInstruction[] = []

    if (data.transaction?.message) {
        for (const instruction of data.transaction.message.instructions) {
            instructions.push(decodeTransactionInstruction(accountKeys, instruction))
        }
    }

    if (data.meta?.innerInstructions?.length) {
        for (const innerInstruction of data.meta.innerInstructions) {
            instructions.push(...innerInstruction.instructions.map((i) => decodeTransactionInstruction(accountKeys, i)))
        }
    }

    return instructions
}
