import type { AccountFilter } from '../backfill/utils/accounts'
import { highlight, type Logger, message } from '@kdt310722/logger'
import { Emitter } from '@kdt310722/utils/event'
import { formatNanoseconds } from '@kdt310722/utils/number'
import { formatDate } from '@kdt310722/utils/time'
import { YellowstoneGeyserClient, type YellowstoneGeyserClientOptions, type YellowstoneGeyserStreamWrapper } from '@kdt-farm/solana-grpc-client/yellowstone'
import { createRawDataInterceptor } from './utils/interceptors'
import { createDefaultSubscribeRequest } from './utils/requests'

export type GeyserEvents = {
    data: (data: Buffer) => void
    unhandledData: (data: unknown) => void
}

export class Geyser extends Emitter<GeyserEvents, true> {
    protected readonly client: YellowstoneGeyserClient
    protected readonly stream: YellowstoneGeyserStreamWrapper

    public constructor(protected readonly logger: Logger, endpoint: string, protected readonly filter: AccountFilter, options: YellowstoneGeyserClientOptions = {}) {
        super()

        this.client = new YellowstoneGeyserClient(endpoint, options)
        this.stream = this.createStream(this.client)
    }

    public async subscribe() {
        await this.stream.subscribe().then(() => this.stream.write(createDefaultSubscribeRequest(this.filter)))
    }

    public async unsubscribe(...args: Parameters<YellowstoneGeyserStreamWrapper['close']>) {
        await this.stream.close(...args)
    }

    protected handleData(data: unknown) {
        if (Buffer.isBuffer(data)) {
            return this.emit('data', data)
        }

        return this.emit('unhandledData', data)
    }

    protected createStream(client: YellowstoneGeyserClient) {
        const stream = client.createStream({ callOptions: { interceptors: [createRawDataInterceptor()] }, timeout: { subscribe: 30_000 } })

        stream.on('state', (state) => this.logger.debug(message(() => `Geyser state: ${highlight(state)}`)))
        stream.on('error', (error) => this.logger.error('Geyser error', error))
        stream.on('destroyedStreamError', (error) => this.logger.error('Geyser error on destroyed stream', error))
        stream.on('closed', () => this.logger.warn('Geyser stream closed'))
        stream.on('waitForResubscribe', (delay) => this.logger.info(`Resubscribe to geyser stream after: ${highlight(formatNanoseconds(BigInt(delay * 1e6)))}`))
        stream.on('resubscribe', (attempt, retriesLeft) => this.logger.info(`Resubscribing to geyser stream (attempts: ${highlight(attempt)}, retries left: ${highlight(retriesLeft)})...`))
        stream.on('circuitBreakerTripped', (lastResubscribeSuccessTime) => this.logger.warn(`Circuit breaker tripped, last success: ${highlight(formatDate(new Date(lastResubscribeSuccessTime)))}`))
        stream.on('resubscribed', () => this.logger.info('Resubscribed to geyser stream'))
        stream.on('resubscribeAbandoned', (reason) => this.logger.exit(1, 'fatal', `Resubscribe to geyser stream abandoned: ${highlight(reason)}`))
        stream.on('write', (data) => this.logger.debug('Write data to geyser stream', data))
        stream.on('wrote', (data) => this.logger.debug('Wrote data to geyser stream', data))
        stream.on('data', (data) => this.handleData(data))

        return stream
    }
}
