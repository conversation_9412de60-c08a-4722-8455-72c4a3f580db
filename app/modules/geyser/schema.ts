import { isString } from '@kdt310722/utils/string'
import { SlotStatus } from '@kdt-farm/solana-grpc-client/yellowstone'
import z from 'zod'
import { nullish } from '../../utils/schemas/nullish'
import { httpUrl } from '../../utils/schemas/requests'

const slotManagerSchema = z.object({
    maxSlots: nullish(z.number().int().positive()),
    waitTimeout: nullish(z.number().int().positive()),
    idleTimeout: nullish(z.number().int().positive()),
})

const schema = z.object({
    url: httpUrl,
    token: nullish(z.string()),
    tokenMetadataKey: nullish(z.string()),
    slot: slotManagerSchema.default({}),
    commitment: z.nativeEnum(SlotStatus).default(SlotStatus.SLOT_PROCESSED),
})

export const geyserSchema = z.union([httpUrl, schema]).transform((value) => {
    return isString(value) ? schema.parse({ url: value }) : value
})
